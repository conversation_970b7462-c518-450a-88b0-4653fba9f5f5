#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试标题提取修复是否有效
"""

import requests
from bs4 import BeautifulSoup
import re
from pathlib import Path
import sys

# 添加父目录到路径，以便导入配置
sys.path.append(str(Path(__file__).parent / "avmanage"))
from av_config import *

def extract_full_title(item):
    """提取完整的作品标题，优先从title属性获取"""
    try:
        # 方法1: 查找带有title属性的链接元素（最优先）
        title_links = item.find_all('a', title=True)
        for link in title_links:
            title_attr = link.get('title', '').strip()
            # 检查是否是作品标题（长度合理且不是简单的导航文本）
            if (title_attr and 
                len(title_attr) > 15 and  # 作品标题通常较长
                not title_attr in ['続きを読む', 'FANZA', '詳細を見る'] and  # 排除导航文本
                not re.match(r'^[A-Z]{2,5}[-_]?\d{2,5}$', title_attr)):  # 不是番号
                return title_attr
        
        # 方法2: 查找img元素的alt属性（备选方案）
        img_elements = item.find_all('img', alt=True)
        for img in img_elements:
            alt_text = img.get('alt', '').strip()
            if (alt_text and 
                len(alt_text) > 15 and
                not re.match(r'^[A-Z]{2,5}[-_]?\d{2,5}$', alt_text)):
                return alt_text
        
        return None
        
    except Exception as e:
        print(f"提取完整标题时出错: {e}")
        return None

def test_title_extraction():
    """测试标题提取功能"""
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.9,ja;q=0.8,zh-CN;q=0.7',
    }
    
    session = requests.Session()
    session.headers.update(headers)
    
    # 使用配置中的URL
    url = ACTRESS_URL
    print(f"测试URL: {url}")
    
    try:
        response = session.get(url)
        response.raise_for_status()
        
        soup = BeautifulSoup(response.text, 'html.parser')
        archive_items = soup.find_all(class_="archive-list")
        
        print(f"找到 {len(archive_items)} 个作品项")
        
        # 测试前5个作品项
        for i, item in enumerate(archive_items[:5]):
            print(f"\n{'='*60}")
            print(f"测试第 {i+1} 个作品项:")
            print(f"{'='*60}")
            
            # 提取完整标题
            full_title = extract_full_title(item)
            
            # 提取显示的文本（可能被截断）
            display_text = ""
            title_links = item.find_all('a')
            for link in title_links:
                link_text = link.get_text().strip()
                if link_text and len(link_text) > 20:
                    display_text = link_text
                    break
            
            print(f"完整标题: {full_title}")
            print(f"显示文本: {display_text}")
            
            if full_title and display_text:
                if '…' in display_text and '…' not in full_title:
                    print("✅ 成功提取完整标题（修复了省略号问题）")
                elif full_title == display_text:
                    print("ℹ️  标题没有被截断")
                else:
                    print("⚠️  标题提取结果与显示文本不同")
            elif full_title:
                print("✅ 成功提取完整标题")
            else:
                print("❌ 未能提取到完整标题")
            
            # 查找番号
            code_text = ""
            meta_items = item.find_all('li')
            for meta_item in meta_items:
                if 'fa-circle-o' in str(meta_item) or meta_item.find(class_='fa-circle-o'):
                    code_text = meta_item.get_text().strip()
                    if code_text:
                        break
            
            print(f"番号: {code_text}")
    
    except Exception as e:
        print(f"测试时出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🔧 标题提取修复测试")
    print("=" * 60)
    
    test_title_extraction()
    
    print("\n" + "=" * 60)
    print("测试完成！")
    print("=" * 60)
