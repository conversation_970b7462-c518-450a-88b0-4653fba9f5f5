#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
from pathlib import Path

# 基础配置
ACTRESS_URL = "https://av-wiki.net/av-actress/fujimori-riho/"
NORMAL_DIRECTORY = "/Volumes/ghs/日本女优/藤森里穂"
VR_DIRECTORY = "/Volumes/ghs/video/VR/神木麗"
MAGNET_SEARCH_URL = "https://wuqianun.top/"
OUTPUT_DIR = "./output"
INCLUDE_VR = True

# 统一番号文件配置
UNIFIED_CODES_FILE = "all_codes.txt"

# 番号类型配置 - 定义可用的番号类型
AVAILABLE_CODE_TYPES = {
    "normal": "普通作品",
    "vr": "VR作品", 
    "collaborative": "共演作品",
    "collection": "合集作品"
}

# 默认启用的番号类型 - 后续脚本将从统一文件中读取这些类型的番号
# 可选值: "normal", "vr", "collaborative", "collection"
ENABLED_CODE_TYPES = ["normal", "collaborative"]

# get_magnet相关配置
GET_MAGNET_OUTPUT_FILE = "magnets.txt"
GET_MAGNET_TEMP_MISSING_FILE = "temp_missing_codes.txt"

# 日期范围过滤器配置 (YYYY-MM-DD格式)
# 如果设置为 None 或 ("", "")，则不过滤日期。
# 示例: DATE_RANGE_FILTER = ("2021-01-01", "2021-12-31") # 只保留2021年的作品
# 示例: DATE_RANGE_FILTER = ("2022-01-01", "") # 保留2022年1月1日之后的作品
# 示例: DATE_RANGE_FILTER = ("", "2020-12-31") # 保留2020年12月31日之前的作品
DATE_RANGE_FILTER = ("2021-04-01", "") # 2021-04-01疑似hhd800最开始日期

# 多人作品人数阈值配置
# 人数分类规则：
# 1人：单体作品
# 2-4人（≤阈值）：共演作品  
# >4人（>阈值）：合集作品
MULTI_ACTOR_THRESHOLD = 4

def ensure_output_dir():
    """确保输出目录存在"""
    os.makedirs(OUTPUT_DIR, exist_ok=True)
    return Path(OUTPUT_DIR)

def get_file_paths():
    """获取所有文件路径"""
    output_dir = ensure_output_dir()
    
    return {
        # 统一文件路径
        "unified_codes_file": output_dir / UNIFIED_CODES_FILE,
        "unified_file_list": output_dir / "all_files.txt",
        "unified_missing_codes_file": output_dir / "all_missing_codes.txt",
        
        # 其他功能文件
        "magnets_file": output_dir / GET_MAGNET_OUTPUT_FILE,
        "temp_missing_file": output_dir / GET_MAGNET_TEMP_MISSING_FILE,
        "output_dir": output_dir
    }
