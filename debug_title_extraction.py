#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试脚本：分析HTML结构，找出如何正确提取完整的作品标题
"""

import requests
from bs4 import BeautifulSoup
import re
from pathlib import Path
import sys

# 添加父目录到路径，以便导入配置
sys.path.append(str(Path(__file__).parent / "avmanage"))
from av_config import *

def analyze_html_structure():
    """分析HTML结构，找出标题存储位置"""
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.9,ja;q=0.8,zh-CN;q=0.7',
    }
    
    session = requests.Session()
    session.headers.update(headers)
    
    url = ACTRESS_URL
    print(f"分析URL: {url}")
    
    try:
        response = session.get(url)
        response.raise_for_status()
        
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # 查找作品列表项
        archive_items = soup.find_all(class_="archive-list")
        
        print(f"找到 {len(archive_items)} 个作品项")
        
        # 分析前3个作品项的结构
        for i, item in enumerate(archive_items[:3]):
            print(f"\n{'='*60}")
            print(f"分析第 {i+1} 个作品项:")
            print(f"{'='*60}")
            
            # 打印整个item的HTML结构
            print("HTML结构:")
            print(item.prettify()[:1000] + "..." if len(item.prettify()) > 1000 else item.prettify())
            
            print(f"\n{'-'*40}")
            print("所有链接元素分析:")
            print(f"{'-'*40}")
            
            links = item.find_all('a')
            for j, link in enumerate(links):
                href = link.get('href', '')
                title_attr = link.get('title', '')
                link_text = link.get_text().strip()
                
                print(f"链接 {j+1}:")
                print(f"  href: {href}")
                print(f"  title属性: '{title_attr}'")
                print(f"  链接文本: '{link_text}'")
                print(f"  链接文本长度: {len(link_text)}")
                
                # 检查是否包含省略号
                if '…' in link_text:
                    print(f"  ⚠️  包含省略号")
                if '…' in title_attr:
                    print(f"  ⚠️  title属性也包含省略号")
                
                print()
            
            print(f"\n{'-'*40}")
            print("其他可能包含标题的元素:")
            print(f"{'-'*40}")
            
            # 查找其他可能包含标题的元素
            title_candidates = []
            
            # 查找所有带title属性的元素
            elements_with_title = item.find_all(attrs={"title": True})
            for element in elements_with_title:
                title_attr = element.get('title', '')
                if title_attr and len(title_attr) > 10:
                    title_candidates.append(('title属性', element.name, title_attr))
            
            # 查找可能的标题元素
            for tag in ['h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'span', 'div']:
                elements = item.find_all(tag)
                for element in elements:
                    text = element.get_text().strip()
                    if text and len(text) > 20 and not re.match(r'^[A-Z]{2,5}[-_]?\d{2,5}$', text):
                        title_candidates.append((f'{tag}元素', tag, text))
            
            print("标题候选项:")
            for source, tag, text in title_candidates:
                print(f"  {source} ({tag}): '{text[:100]}{'...' if len(text) > 100 else ''}'")
            
            if i < 2:  # 只分析前3个，避免输出太长
                input("按回车继续分析下一个作品项...")
    
    except Exception as e:
        print(f"分析时出错: {e}")
        import traceback
        traceback.print_exc()

def test_specific_selectors():
    """测试特定的CSS选择器"""
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.9,ja;q=0.8,zh-CN;q=0.7',
    }
    
    session = requests.Session()
    session.headers.update(headers)
    
    url = ACTRESS_URL
    print(f"\n{'='*60}")
    print("测试特定选择器")
    print(f"{'='*60}")
    
    try:
        response = session.get(url)
        response.raise_for_status()
        
        soup = BeautifulSoup(response.text, 'html.parser')
        archive_items = soup.find_all(class_="archive-list")
        
        if archive_items:
            item = archive_items[0]  # 测试第一个作品项
            
            # 测试各种可能的选择器
            selectors_to_test = [
                ('a[title]', '带title属性的链接'),
                ('a[href*="/av/"]', '作品详情页链接'),
                ('.post-title a', '文章标题链接'),
                ('.entry-title a', '条目标题链接'),
                ('h2 a', 'h2标题链接'),
                ('h3 a', 'h3标题链接'),
                ('.title a', '标题类链接'),
                ('a[href*="av-wiki.net"]', '站内链接'),
            ]
            
            print("测试各种CSS选择器:")
            for selector, description in selectors_to_test:
                elements = item.select(selector)
                print(f"\n{description} ({selector}):")
                if elements:
                    for i, element in enumerate(elements):
                        title_attr = element.get('title', '')
                        text = element.get_text().strip()
                        href = element.get('href', '')
                        print(f"  元素 {i+1}:")
                        print(f"    href: {href}")
                        print(f"    title: '{title_attr}'")
                        print(f"    text: '{text}'")
                else:
                    print("  未找到匹配元素")
    
    except Exception as e:
        print(f"测试时出错: {e}")

def save_html_sample():
    """保存HTML样本用于离线分析"""
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.9,ja;q=0.8,zh-CN;q=0.7',
    }
    
    session = requests.Session()
    session.headers.update(headers)
    
    url = ACTRESS_URL
    
    try:
        response = session.get(url)
        response.raise_for_status()
        
        # 保存完整HTML
        with open('debug_html_sample.html', 'w', encoding='utf-8') as f:
            f.write(response.text)
        
        print("HTML样本已保存到 debug_html_sample.html")
        
        # 保存第一个作品项的HTML
        soup = BeautifulSoup(response.text, 'html.parser')
        archive_items = soup.find_all(class_="archive-list")
        
        if archive_items:
            with open('debug_first_item.html', 'w', encoding='utf-8') as f:
                f.write(archive_items[0].prettify())
            
            print("第一个作品项HTML已保存到 debug_first_item.html")
    
    except Exception as e:
        print(f"保存HTML样本时出错: {e}")

if __name__ == "__main__":
    print("🔍 HTML结构分析工具")
    print("用于调试作品标题提取问题")
    
    # 保存HTML样本
    save_html_sample()
    
    # 分析HTML结构
    analyze_html_structure()
    
    # 测试特定选择器
    test_specific_selectors()
    
    print(f"\n{'='*60}")
    print("分析完成！")
    print("请检查生成的HTML文件以进行进一步分析")
    print(f"{'='*60}")
