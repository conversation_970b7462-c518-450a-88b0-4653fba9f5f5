#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试yamate-ria数据获取问题
"""

import requests
from bs4 import BeautifulSoup
import re
from pathlib import Path
import sys
from datetime import datetime

# 添加父目录到路径，以便导入配置
sys.path.append(str(Path(__file__).parent / "avmanage"))
from av_config import *

def is_date_in_range(release_date_str, date_range_filter_tuple):
    """检查日期是否在指定的日期范围内"""
    if not date_range_filter_tuple or (not date_range_filter_tuple[0] and not date_range_filter_tuple[1]):
        return True

    if not release_date_str:
        return False

    try:
        item_date = datetime.strptime(release_date_str, "%Y-%m-%d").date()
    except ValueError:
        print(f"警告：作品发行日期 '{release_date_str}' 格式无效，无法进行日期范围比较。将跳过此项。")
        return False

    start_date_str, end_date_str = date_range_filter_tuple
    
    start_date_obj = None
    if start_date_str:
        try:
            start_date_obj = datetime.strptime(start_date_str, "%Y-%m-%d").date()
        except ValueError:
            print(f"警告：配置的开始日期 '{start_date_str}' 格式无效，此日期边界将被忽略。")

    end_date_obj = None
    if end_date_str:
        try:
            end_date_obj = datetime.strptime(end_date_str, "%Y-%m-%d").date()
        except ValueError:
            print(f"警告：配置的结束日期 '{end_date_str}' 格式无效，此日期边界将被忽略。")

    if start_date_obj and item_date < start_date_obj:
        return False
    
    if end_date_obj and item_date > end_date_obj:
        return False
        
    return True

def debug_yamate_ria():
    """调试yamate-ria数据获取"""
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.9,ja;q=0.8,zh-CN;q=0.7',
    }
    
    session = requests.Session()
    session.headers.update(headers)
    
    url = ACTRESS_URL
    print(f"调试URL: {url}")
    print(f"日期过滤范围: {DATE_RANGE_FILTER}")
    
    try:
        response = session.get(url)
        response.raise_for_status()
        
        soup = BeautifulSoup(response.text, 'html.parser')
        archive_items = soup.find_all(class_="archive-list")
        
        print(f"找到 {len(archive_items)} 个作品项")
        
        valid_items = 0
        filtered_items = 0
        
        # 分析前5个作品项
        for i, item in enumerate(archive_items[:5]):
            print(f"\n{'='*50}")
            print(f"分析第 {i+1} 个作品项:")
            print(f"{'='*50}")
            
            # 查找番号
            code_text = ""
            meta_items = item.find_all('li')
            for meta_item in meta_items:
                if 'fa-circle-o' in str(meta_item) or meta_item.find(class_='fa-circle-o'):
                    code_text = meta_item.get_text().strip()
                    if code_text:
                        break
            
            # 查找发布日期
            release_date = ""
            date_elements = item.find_all(class_='fa-clock-o')
            if date_elements:
                date_parent = date_elements[0].parent
                if date_parent:
                    release_date = date_parent.get_text().strip()
            
            # 查找完整标题
            full_title = ""
            title_links = item.find_all('a', title=True)
            for link in title_links:
                title_attr = link.get('title', '').strip()
                if title_attr and len(title_attr) > 15:
                    full_title = title_attr
                    break
            
            print(f"番号: {code_text}")
            print(f"发布日期: {release_date}")
            print(f"完整标题: {full_title}")
            
            # 检查日期过滤
            if code_text:
                date_valid = is_date_in_range(release_date, DATE_RANGE_FILTER)
                print(f"日期过滤结果: {'通过' if date_valid else '被过滤'}")
                
                if date_valid:
                    valid_items += 1
                    print("✅ 该作品项会被包含在结果中")
                else:
                    filtered_items += 1
                    print("❌ 该作品项被日期过滤器排除")
        
        print(f"\n{'='*50}")
        print(f"总结:")
        print(f"{'='*50}")
        print(f"总作品项: {len(archive_items)}")
        print(f"前5项中通过过滤的: {valid_items}")
        print(f"前5项中被过滤的: {filtered_items}")
        
    except Exception as e:
        print(f"调试时出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🔍 yamate-ria数据获取调试")
    print("=" * 60)
    
    debug_yamate_ria()
    
    print("\n" + "=" * 60)
    print("调试完成！")
    print("=" * 60)
